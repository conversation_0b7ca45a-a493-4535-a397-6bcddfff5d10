import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Settings, Zap, Lightbulb, Fan, Thermometer } from "lucide-react";
import { getUnitIOSpec, getOutputTypes, getInputFunctions } from "@/constants";
import { useProjectDetail } from "@/contexts/project-detail-context";

export function IOConfigDialog({ open, onOpenChange, item = null }) {
  const { projectItems } = useProjectDetail();

  const [lightingItems, setLightingItems] = useState([]);
  const [airconItems, setAirconItems] = useState([]);
  const [inputConfigs, setInputConfigs] = useState([]);
  const [outputConfigs, setOutputConfigs] = useState([]);

  // Get I/O specifications for the unit - memoized to prevent recalculation
  const ioSpec = useMemo(() => {
    return item?.type ? getUnitIOSpec(item.type) : null;
  }, [item?.type]);

  const outputTypes = useMemo(() => {
    return item?.type ? getOutputTypes(item.type) : [];
  }, [item?.type]);

  // Load lighting and aircon data from projectItems
  useEffect(() => {
    if (projectItems?.lighting) {
      setLightingItems(projectItems.lighting);
    }
    if (projectItems?.aircon) {
      setAirconItems(projectItems.aircon);
    }
  }, [projectItems]);

  // Helper function to get output label (moved outside useEffect to avoid dependency issues)
  const getOutputLabel = (type) => {
    switch (type) {
      case "relay":
        return "Relay";
      case "dimmer":
        return "Dimmer";
      case "ao":
        return "Analog";
      case "ac":
        return "Aircon";
      default:
        return type;
    }
  };

  // Initialize input/output configurations
  useEffect(() => {
    if (!ioSpec) return;

    // Initialize input configurations
    const inputs = [];
    for (let i = 0; i < ioSpec.inputs; i++) {
      inputs.push({
        index: i,
        name: `Input ${i + 1}`,
        lightingId: null,
        functionValue: 0, // Default to "Unused"
      });
    }
    setInputConfigs(inputs);

    // Initialize output configurations
    const outputs = [];
    let outputIndex = 0;

    outputTypes.forEach(({ type, count }) => {
      for (let i = 0; i < count; i++) {
        outputs.push({
          index: outputIndex++,
          name: `${getOutputLabel(type)} ${i + 1}`,
          type: type,
          deviceId: null,
        });
      }
    });
    setOutputConfigs(outputs);
  }, [ioSpec, outputTypes]);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    // TODO: Implement I/O configuration save logic
    console.log("I/O Config for unit:", item);
    console.log("Input configs:", inputConfigs);
    console.log("Output configs:", outputConfigs);
    handleClose();
  };

  const getOutputIcon = (type) => {
    switch (type) {
      case "relay":
        return <Zap className="h-4 w-4" />;
      case "dimmer":
        return <Lightbulb className="h-4 w-4" />;
      case "ao":
        return <Fan className="h-4 w-4" />;
      case "ac":
        return <Thermometer className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  // Helper functions for input/output configuration
  const handleInputLightingChange = (inputIndex, lightingId) => {
    setInputConfigs((prev) =>
      prev.map((config) =>
        config.index === inputIndex ? { ...config, lightingId } : config
      )
    );
  };

  const handleInputFunctionChange = (inputIndex, functionValue) => {
    setInputConfigs((prev) =>
      prev.map((config) =>
        config.index === inputIndex ? { ...config, functionValue } : config
      )
    );
  };

  const handleOutputDeviceChange = (outputIndex, deviceId) => {
    setOutputConfigs((prev) =>
      prev.map((config) =>
        config.index === outputIndex ? { ...config, deviceId } : config
      )
    );
  };

  // Prepare combobox options - memoized to prevent recalculation
  const lightingOptions = useMemo(() => {
    return lightingItems.map((item) => ({
      value: item.id.toString(),
      label: `${item.name || "Unnamed"} (${item.address})`,
    }));
  }, [lightingItems]);

  const airconOptions = useMemo(() => {
    return airconItems.map((item) => ({
      value: item.id.toString(),
      label: `${item.name || "Unnamed"} (${item.address})`,
    }));
  }, [airconItems]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[70%] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            I/O Configuration
          </DialogTitle>
          <DialogDescription>
            Configure input/output settings for {item?.type || "unit"}:{" "}
            {item?.serial_no || "N/A"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {ioSpec ? (
            <div className="grid grid-cols-2 gap-6">
              {/* Input Configuration - Left Side */}
              <Card className="h-[600px] flex flex-col">
                <CardHeader className="flex-shrink-0">
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Input Configuration
                    <Badge variant="secondary" className="ml-auto">
                      {ioSpec.inputs} Inputs
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-hidden">
                  <ScrollArea className="h-full">
                    {inputConfigs.length > 0 ? (
                      <div className="space-y-3 pr-4">
                        {inputConfigs.map((config) => {
                          const availableFunctions = getInputFunctions(
                            item?.type,
                            config.index
                          );
                          const functionOptions = availableFunctions.map(
                            (func) => ({
                              value: func.value.toString(),
                              label: func.label,
                            })
                          );

                          return (
                            <div
                              key={config.index}
                              className="space-y-3 p-4 border rounded-lg flex gap-4"
                            >
                              <div className="space-y-2 w-1/2">
                                <Label className="text-sm font-medium">
                                  {config.name}
                                </Label>
                                <Combobox
                                  options={functionOptions}
                                  value={config.functionValue.toString()}
                                  onValueChange={(value) =>
                                    handleInputFunctionChange(
                                      config.index,
                                      parseInt(value)
                                    )
                                  }
                                  placeholder="Select function..."
                                  emptyText="No functions available"
                                />
                              </div>
                              <div className="space-y-2 w-1/2">
                                <Label className="text-sm text-muted-foreground">
                                  Lighting
                                </Label>
                                <Combobox
                                  options={lightingOptions}
                                  value={config.lightingId?.toString() || ""}
                                  onValueChange={(value) =>
                                    handleInputLightingChange(
                                      config.index,
                                      value ? parseInt(value) : null
                                    )
                                  }
                                  placeholder="Select lighting..."
                                  emptyText="No lighting found"
                                />
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        <p>No inputs available for this unit type.</p>
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>

              {/* Output Configuration - Right Side */}
              <Card className="h-[600px] flex flex-col">
                <CardHeader className="flex-shrink-0">
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Output Configuration
                    <Badge variant="secondary" className="ml-auto">
                      {ioSpec.totalOutputs} Outputs
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-hidden">
                  <ScrollArea className="h-full">
                    {outputConfigs.length > 0 ? (
                      <div className="space-y-3 pr-4">
                        {outputConfigs.map((config) => {
                          const isAircon = config.type === "ac";
                          const deviceOptions = isAircon
                            ? airconOptions
                            : lightingOptions;

                          return (
                            <div
                              key={config.index}
                              className="p-4 border rounded-lg flex gap-4 justify-center items-center"
                            >
                              <Label className="text-sm font-medium flex-shrink-0">
                                {getOutputIcon(config.type)}
                                {config.name}
                              </Label>
                              <Combobox
                                className="flex-1"
                                options={deviceOptions}
                                value={config.deviceId?.toString() || ""}
                                onValueChange={(value) =>
                                  handleOutputDeviceChange(
                                    config.index,
                                    value ? parseInt(value) : null
                                  )
                                }
                                placeholder={`Select ${
                                  isAircon ? "aircon" : "lighting"
                                }...`}
                                emptyText={`No ${
                                  isAircon ? "aircon" : "lighting"
                                } found`}
                              />
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        <p>No outputs available for this unit type.</p>
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No I/O Specifications</p>
              <p className="text-sm">
                Unable to load I/O specifications for this unit type.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Configuration</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
